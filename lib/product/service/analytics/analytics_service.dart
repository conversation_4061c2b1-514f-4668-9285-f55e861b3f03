import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Centralized analytics service for Facebook App Events
/// Implements funnel tracking with duplicate prevention
class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  static const String _funnelPrefix = 'funnel_event_';
  static const String _sessionPrefix = 'session_';

  late SharedPreferences _prefs;
  bool _isInitialized = false;

  /// Initialize the analytics service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      _isInitialized = true;

      if (kDebugMode) {
        debugPrint('AnalyticsService: Initialized successfully');
      }
    } catch (e) {
      debugPrint('AnalyticsService: Failed to initialize - $e');
    }
  }

  /// Check if an event has already been logged in this session
  Future<bool> _hasEventBeenLogged(String eventName) async {
    if (!_isInitialized) await initialize();

    final sessionId = await _getCurrentSessionId();
    final key = '$_funnelPrefix$sessionId$eventName';
    return _prefs.getBool(key) ?? false;
  }

  /// Mark an event as logged for this session
  Future<void> _markEventAsLogged(String eventName) async {
    if (!_isInitialized) await initialize();

    final sessionId = await _getCurrentSessionId();
    final key = '$_funnelPrefix$sessionId$eventName';
    await _prefs.setBool(key, true);
  }

  /// Get or create current session ID
  Future<String> _getCurrentSessionId() async {
    const sessionKey = '${_sessionPrefix}current';
    String? sessionId = _prefs.getString(sessionKey);

    if (sessionId == null) {
      sessionId = DateTime.now().millisecondsSinceEpoch.toString();
      await _prefs.setString(sessionKey, sessionId);
    }

    return sessionId;
  }

  /// Reset session (call on app restart or new user login)
  Future<void> resetSession() async {
    if (!_isInitialized) await initialize();

    const sessionKey = '${_sessionPrefix}current';
    final newSessionId = DateTime.now().millisecondsSinceEpoch.toString();
    await _prefs.setString(sessionKey, newSessionId);

    if (kDebugMode) {
      debugPrint('AnalyticsService: Session reset - $newSessionId');
    }
  }

  /// Log a funnel event (prevents duplicates within same session)
  Future<void> logFunnelEvent(String eventName, {Map<String, dynamic>? parameters}) async {
    try {
      if (!_isInitialized) await initialize();

      // Check if event already logged in this session
      if (await _hasEventBeenLogged(eventName)) {
        if (kDebugMode) {
          debugPrint('AnalyticsService: Event $eventName already logged in this session');
        }
        return;
      }

      // Log to Facebook App Events
      await FacebookAppEvents().logEvent(
        name: eventName,
        parameters: parameters ?? {},
      );

      // Mark as logged
      await _markEventAsLogged(eventName);

      if (kDebugMode) {
        debugPrint('AnalyticsService: Logged funnel event - $eventName');
        if (parameters != null) {
          debugPrint('AnalyticsService: Parameters - $parameters');
        }
      }
    } catch (e) {
      debugPrint('AnalyticsService: Failed to log funnel event $eventName - $e');
    }
  }

  /// Log a regular event (allows duplicates)
  Future<void> logEvent(String eventName, {Map<String, dynamic>? parameters}) async {
    try {
      if (!_isInitialized) await initialize();

      await FacebookAppEvents().logEvent(
        name: eventName,
        parameters: parameters ?? {},
      );

      if (kDebugMode) {
        debugPrint('AnalyticsService: Logged event - $eventName');
        if (parameters != null) {
          debugPrint('AnalyticsService: Parameters - $parameters');
        }
      }
    } catch (e) {
      debugPrint('AnalyticsService: Failed to log event $eventName - $e');
    }
  }

  // MARK: - Funnel Events (One-time per session)

  /// Log app start event
  Future<void> logAppStart() async {
    await logFunnelEvent('app_started');
  }

  /// Log sign up page view
  Future<void> logSignUpPageView() async {
    await logFunnelEvent('sign_up_page_viewed');
  }

  /// Log account creation
  Future<void> logAccountCreated({String? method}) async {
    await logFunnelEvent('account_created', parameters: {
      if (method != null) 'method': method,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Log username determination
  Future<void> logUsernameDetermined({String? username}) async {
    await logFunnelEvent('username_determined', parameters: {
      if (username != null) 'username_length': username.length,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Log profile photo page view
  Future<void> logProfilePhotoPageView() async {
    await logFunnelEvent('profile_photo_page_viewed');
  }

  /// Log loot box view
  Future<void> logLootBoxView(int boxNumber) async {
    await logFunnelEvent('loot_box_${boxNumber}_viewed', parameters: {
      'box_number': boxNumber,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  // MARK: - Activity Events (Allow duplicates)

  /// Log battle activity
  Future<void> logBattleActivity({
    String? battleId,
    String? result,
    int? coinsEarned,
  }) async {
    await logEvent('battle_activity', parameters: {
      if (battleId != null) 'battle_id': battleId,
      if (result != null) 'result': result,
      if (coinsEarned != null) 'coins_earned': coinsEarned,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Log movie selling
  Future<void> logMovieSelling({
    String? movieId,
    String? movieTitle,
    int? price,
  }) async {
    await logEvent('movie_sold', parameters: {
      if (movieId != null) 'movie_id': movieId,
      if (movieTitle != null) 'movie_title': movieTitle,
      if (price != null) 'price': price,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Log movie taking/collecting
  Future<void> logMovieTaking({
    String? movieId,
    String? movieTitle,
    String? source, // 'drop', 'battle', 'purchase', etc.
  }) async {
    await logEvent('movie_taken', parameters: {
      if (movieId != null) 'movie_id': movieId,
      if (movieTitle != null) 'movie_title': movieTitle,
      if (source != null) 'source': source,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Debug method to test all analytics events (only in debug mode)
  Future<void> testAllEvents() async {
    if (!kDebugMode) return;

    debugPrint('🧪 Testing all analytics events...');

    // Test funnel events
    await logAppStart();
    await logSignUpPageView();
    await logAccountCreated(method: 'test');
    await logUsernameDetermined(username: 'test_user');
    await logProfilePhotoPageView();
    await logLootBoxView(1);
    await logLootBoxView(2);
    await logLootBoxView(3);

    // Test activity events
    await logBattleActivity(battleId: 'test_battle', result: 'won', coinsEarned: 20);
    await logMovieSelling(movieId: 'test_movie', movieTitle: 'Test Movie', price: 100);
    await logMovieTaking(movieId: 'test_movie', movieTitle: 'Test Movie', source: 'test');

    debugPrint('🧪 All analytics events tested successfully!');
  }

  /// Clear old session data (call periodically to prevent storage bloat)
  Future<void> clearOldSessions({int daysToKeep = 7}) async {
    if (!_isInitialized) await initialize();

    final cutoffTime = DateTime.now().subtract(Duration(days: daysToKeep));
    final keys = _prefs.getKeys();

    for (final key in keys) {
      if (key.startsWith(_funnelPrefix)) {
        // Extract session ID from key
        final parts = key.split(_funnelPrefix);
        if (parts.length > 1) {
          final sessionPart = parts[1];
          final sessionIdEnd = sessionPart.contains('app_')
              ? sessionPart.indexOf('app_')
              : sessionPart.contains('sign_')
                  ? sessionPart.indexOf('sign_')
                  : sessionPart.contains('account_')
                      ? sessionPart.indexOf('account_')
                      : sessionPart.contains('username_')
                          ? sessionPart.indexOf('username_')
                          : sessionPart.contains('profile_')
                              ? sessionPart.indexOf('profile_')
                              : sessionPart.contains('loot_')
                                  ? sessionPart.indexOf('loot_')
                                  : -1;

          if (sessionIdEnd > 0) {
            final sessionId = sessionPart.substring(0, sessionIdEnd);
            try {
              final sessionTime = DateTime.fromMillisecondsSinceEpoch(int.parse(sessionId));
              if (sessionTime.isBefore(cutoffTime)) {
                await _prefs.remove(key);
              }
            } catch (e) {
              // Invalid session ID format, remove it
              await _prefs.remove(key);
            }
          }
        }
      }
    }

    if (kDebugMode) {
      debugPrint('AnalyticsService: Cleared old session data');
    }
  }
}
