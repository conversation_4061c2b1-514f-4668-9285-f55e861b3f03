name: movie_map
description: "A new Flutter project."
publish_to: 'none'
version: 1.0.7+7

environment:
  sdk: '>=3.4.4 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # UI development
  gap: ^3.0.1
  
  # Translate
  easy_localization: ^3.0.4

  # Location Services
  geolocator: ^11.0.0
  google_maps_flutter: ^2.5.3
  flutter_polyline_points: ^2.0.0
  google_places_flutter: ^2.0.8
  
  # State Management
  provider: ^6.1.1
  
  #Movie Database
  tmdb_api: ^2.2.0

  #Caching Data
  shared_preferences: ^2.3.2
  
  # Networking
  http: ^1.2.2

  # Firebase Services
  firebase_core: ^3.14.0
  firebase_auth: ^5.6.0

  # Authentication
  google_sign_in: ^6.2.1
  cloud_firestore: ^5.6.9
  dotted_border: ^2.1.0
  intl: ^0.20.2
  share_plus: ^10.0.2

  # splash screen
  flutter_native_splash: ^2.4.1
  image_picker: ^1.0.0
  permission_handler: ^11.3.1
  firebase_storage: ^12.4.7
  shimmer: ^3.0.0
  cached_network_image: ^3.4.1
  webview_flutter: ^4.10.0
  flutter_dotenv: ^5.2.1
  in_app_purchase: ^3.2.0
  auto_size_text: ^3.0.0
  vibration: ^3.0.0
  facebook_app_events: ^0.20.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter_native_splash:
  color: "#091F5B"
  image: assets/images/splash/movie_map_splash.png
  android_12:
    image: assets/images/splash/movie_map_splash.png
    color: "#091F5B"

flutter:
  uses-material-design: true
  assets:
    - assets/translations/
    - assets/style/dark-map-style.json
    - assets/onboard.png
    - assets/movie_icon.png
    - .env
