import 'dart:math';
import 'dart:ui';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:movie_map/core/helper/costum_button.dart';
import 'package:movie_map/feature/dropping/dropped_movie_view.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/service/animation_manager.dart';
import 'package:movie_map/product/service/cache/random_movies_service.dart';
import 'package:movie_map/product/state/provider/user_manager_provider.dart';
import 'package:movie_map/product/theme/theme.dart';
import 'package:movie_map/product/utility/constants/constants.dart';
import 'package:tmdb_api/tmdb_api.dart';
import 'package:movie_map/product/service/analytics/analytics_service.dart';
part 'film_strip_component.dart';

class DropMovieView extends StatefulWidget {
  final double? extraDistance;
  final MarkerId markerId;
  final bool isOnboardingReward;
  final int? onboardingMovieIndex;
  final int? totalOnboardingMovies;

  const DropMovieView({
    super.key,
    this.extraDistance,
    required this.markerId,
    this.isOnboardingReward = false,
    this.onboardingMovieIndex,
    this.totalOnboardingMovies,
  });

  @override
  State<DropMovieView> createState() => _DropMovieViewState();
}

class _DropMovieViewState extends State<DropMovieView> with TickerProviderStateMixin {
  final GlobalKey<_FilmStripState> _filmStripKey = GlobalKey<_FilmStripState>();

  late AnimationManager _animationManager;
  late Animation<Offset> _positionAnimation;
  List<String> moviesCoverList = [];
  MovieModel? movie;
  bool isLoading = true;
  // bool _hasOpenedDrop = false;

  @override
  void initState() {
    super.initState();
    initAnimation();
    fetchSpinMovies();

    // Log loot box view for onboarding rewards
    if (widget.isOnboardingReward && widget.onboardingMovieIndex != null) {
      AnalyticsService().logLootBoxView(widget.onboardingMovieIndex!);
    }
  }

  void initAnimation() {
    _animationManager = AnimationManager(vsync: this);
    _positionAnimation = _animationManager.createPositionAnimation(
      duration: const Duration(milliseconds: 800),
      begin: const Offset(-15, -15),
      end: const Offset(0, 0),
    );

    _animationManager.positionController.addStatusListener(
      (status) {
        if (status == AnimationStatus.completed) {
          startPhotoTransition();
        }
      },
    );
  }

  Future<void> fetchSpinMovies() async {
    try {
      setState(() {
        isLoading = true;
      });

      movie = await RandomMoviesService().fetchRandomMovie();
      debugPrint('Movie: ${movie!.title}');
      moviesCoverList = await RandomMoviesService().fetchRecommendedMovieIds(movie!.id);
      debugPrint('$moviesCoverList');

      setState(() {
        isLoading = false;
      });
    } catch (e) {
      debugPrint('Error fetching recommended movies: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> startPositionAnimation() async {
    _animationManager.positionController.forward(from: 0);
  }

  void startPhotoTransition() async {
    const imageDisplayDuration = 300;

    for (int i = 0; i < moviesCoverList.length; i++) {
      Future.delayed(Duration(milliseconds: i * imageDisplayDuration), () {
        if (mounted) {
          if (moviesCoverList.isNotEmpty) {
            moviesCoverList.removeAt(moviesCoverList.length - 1);
            setState(() {});
            debugPrint('Removed: $i');
          }
        }
      });
    }

    _filmStripKey.currentState?.startAnimation();
    _filmStripKey.currentState?._controller?.addStatusListener(
      (status) {
        if (status == AnimationStatus.completed) {
          int randomPrice = determineRandomPrice();

          Navigator.pushReplacement(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) => DroppedMovieView(
                movie: movie!,
                markerId: widget.markerId,
                price: randomPrice,
                isOnboardingReward: widget.isOnboardingReward,
                onboardingMovieIndex: widget.onboardingMovieIndex,
                totalOnboardingMovies: widget.totalOnboardingMovies,
              ),
              transitionsBuilder: (context, animation, secondaryAnimation, child) {
                const begin = Offset(0.0, 1.0);
                const end = Offset.zero;
                const curve = Curves.easeInOutCubic;

                var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
                var offsetAnimation = animation.drive(tween);

                return SlideTransition(
                  position: offsetAnimation,
                  child: child,
                );
              },
            ),
          );
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
      ),
      body: Center(
        child: isLoading || movie == null || moviesCoverList.isEmpty
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: AppTheme.main,
                  ),
                  const Gap(16),
                  const Text(
                    'Loading your movie...',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              )
            : Column(
                children: [
                  Gap(screenHeight * 0.01),
                  // Hide FilmStrip during onboarding until drop is opened
                  // if (!widget.isOnboardingReward || _hasOpenedDrop)
                  FilmStrip(
                    key: _filmStripKey,
                  ),
                  Gap(screenHeight * 0.03),
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      AnimatedBuilder(
                        animation: _filmStripKey.currentState?._controller ?? AnimationController(vsync: this),
                        builder: (context, child) {
                          return Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(24),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  blurRadius: 15,
                                  offset: const Offset(0, 8),
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(24),
                              child: ImageFiltered(
                                imageFilter: ImageFilter.blur(
                                  sigmaX: 12 - (60 * (_filmStripKey.currentState?._controller?.value ?? 0)),
                                  sigmaY: 12 - (60 * (_filmStripKey.currentState?._controller?.value ?? 0)),
                                ),
                                child: Opacity(
                                  opacity: 1.0,
                                  child: SizedBox(
                                    height: 300,
                                    width: 300,
                                    child: Stack(
                                      clipBehavior: Clip.none,
                                      children: moviesCoverList.asMap().entries.map((entry) {
                                        int index = entry.key;
                                        // TODO: Burada bayağı kalitesiz bir kod var gibi. Bu işlem için buradan 250 kere falan geçiyor sanırım.

                                        return ClipRRect(
                                          borderRadius: BorderRadius.circular(24),
                                          child: Image.network(
                                            'https://image.tmdb.org/t/p/w500${moviesCoverList[index]}',
                                            height: 300,
                                            width: 300,
                                            fit: BoxFit.cover,
                                          ),
                                        );
                                      }).toList(),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                      AnimatedBuilder(
                        animation: _positionAnimation,
                        builder: (context, child) {
                          return Positioned(
                            bottom: _positionAnimation.value.dy,
                            left: _positionAnimation.value.dx,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(24),
                              child: BackdropFilter(
                                filter: ImageFilter.blur(sigmaX: 12, sigmaY: 12),
                                child: Container(
                                  height: 300,
                                  width: 300,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        Colors.grey.shade700.withOpacity(0.85),
                                        Colors.grey.shade800.withOpacity(0.95),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(24),
                                    border: Border.all(
                                      color: Colors.white.withOpacity(0.1),
                                      width: 0.5,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                  Gap(screenHeight * 0.06),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32.0),
                    child: Stack(
                      children: [
                        Text(
                          widget.isOnboardingReward ? 'Collect Movies 🎁' : 'What\'s inside? 🎬',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w500,
                            height: 1.2,
                            letterSpacing: -0.5,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Gap(screenHeight * 0.06),
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 24),
                    child: CostumButton(
                      onPressed: () async {
                        setState(() {
                          isLoading = true;
                          // _hasOpenedDrop = true;
                        });

                        final User? user = FirebaseAuth.instance.currentUser;
                        if (widget.extraDistance == null || widget.isOnboardingReward) {
                          await startPositionAnimation();
                        } else {
                          final state = await UserManagerProvider().spendMoney(
                            user!.uid,
                            widget.extraDistance! <= 600 ? (widget.extraDistance! * 1.2).toInt() : 740,
                          );
                          if (state == 'NotEnough') {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Container(
                                  padding: const EdgeInsets.symmetric(vertical: 8),
                                  child: const Text(
                                    'You don\'t have enough money to open this drop.',
                                    style: TextStyle(fontSize: 16),
                                  ),
                                ),
                                backgroundColor: Colors.red.shade800,
                                behavior: SnackBarBehavior.floating,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            );
                            Navigator.pop(context);
                          } else {
                            await startPositionAnimation();
                          }
                        }
                        setState(() {
                          isLoading = false;
                        });
                      },
                      text: widget.extraDistance == null ? 'Open Drop' : 'Or open now for 💰 ${widget.extraDistance! <= 600 ? (widget.extraDistance! * 1.2).toInt() : 740}',
                      backgroundColor: AppTheme.main,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  TMDB moviesList = TMDB(
    ApiKeys(ApiConstants.instance.tmdbApiKey, ApiConstants.instance.tmdbApiReadToken),
    logConfig: const ConfigLogger(showLogs: true, showErrorLogs: true),
  );

  Future<List> fetchRecommendedMovies(int movieId, int maxMovies) async {
    int currentPage = 1;
    int totalPages = 1;
    List allMovies = [];

    while (currentPage <= totalPages && allMovies.length < maxMovies) {
      Map recommendedMovies = await moviesList.v3.movies.getRecommended(movieId, language: 'en', page: currentPage);
      totalPages = recommendedMovies['total_pages'];
      List movies = recommendedMovies['results'];
      allMovies.addAll(movies.length + allMovies.length > maxMovies ? movies.sublist(0, maxMovies - allMovies.length) : movies);
      currentPage++;
    }
    return allMovies;
  }

  @override
  void dispose() {
    _animationManager.dispose();
    super.dispose();
  }

  int determineRandomPrice() {
    // First, determine difficulty based on probability
    final random = Random();
    final probability = random.nextDouble() * 100; // 0-100

    final PricePossibility difficulty;
    if (probability < 50) {
      difficulty = PricePossibility.easy;
    } else if (probability < 80) {
      difficulty = PricePossibility.medium;
    } else {
      difficulty = PricePossibility.hard;
    }

    // Then generate price based on difficulty
    switch (difficulty) {
      case PricePossibility.easy:
        return random.nextInt(100) + 50; // 50-150
      case PricePossibility.medium:
        return random.nextInt(100) + 150; // 150-250
      case PricePossibility.hard:
        return random.nextInt(100) + 250; // 250-350
    }
  }
}

enum PricePossibility { easy, medium, hard }
