import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:movie_map/feature/home/<USER>/home_view.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/state/provider/user_manager_provider.dart';
import 'package:provider/provider.dart';

class WelcomeLootBoxView extends StatefulWidget {
  const WelcomeLootBoxView({super.key, required bool isDebugMode});

  @override
  State<WelcomeLootBoxView> createState() => _WelcomeLootBoxViewState();
}

class _WelcomeLootBoxViewState extends State<WelcomeLootBoxView> {
  final String _welcomeLootBox = 'Welcome Loot Box';
  final String _welcomeText = 'Welcome to Movie Map! 🎉\nBefore we start, let\'s add some movies to your collection.';
  final String _continueButtonText = 'Continue';

  late final userManagerProvider = context.read<UserManagerProvider>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 60,
        actions: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: GestureDetector(
              onTap: () async {
                await _finishLootBox();
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(
                    builder: (_) => const HomeView(),
                  ),
                  (route) => false,
                );
              },
              child: const Text(
                'Skip',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Column(
          children: [
            const Gap(20),
            Text(
              _welcomeLootBox,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Gap(20),
            Text(
              _welcomeText,
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
            const Gap(20),
            Expanded(
              child: StreamBuilder<QuerySnapshot>(
                stream: FirebaseFirestore.instance.collection('movies').limit(5).snapshots(),
                builder: (context, snapshot) {
                  if (!snapshot.hasData) return const SizedBox.shrink();

                  final movies = snapshot.data?.docs.map((doc) => MovieModel.fromMap(doc.data() as Map<String, dynamic>)).toList() ?? [];

                  return ListView.builder(
                    itemCount: movies.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Row(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: NetworkImage('https://image.tmdb.org/t/p/w500${movies[index].posterPath}'),
                                ),
                              ),
                              height: 100,
                              width: 70,
                            ),
                            const Gap(10),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    movies[index].title,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const Gap(4),
                                  Text(
                                    movies[index].releaseDate,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  );
                },
              ),
            ),
            const Gap(20),
            ElevatedButton(
              onPressed: () async {
                await _finishLootBox();
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(
                    builder: (_) => const HomeView(),
                  ),
                  (route) => false,
                );
              },
              style: ElevatedButton.styleFrom(
                fixedSize: const Size(double.infinity, 54),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: Text(
                _continueButtonText,
                style: const TextStyle(fontSize: 20),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _finishLootBox() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      await userManagerProvider.addMoney(user.uid, 100);
    }
  }
}
