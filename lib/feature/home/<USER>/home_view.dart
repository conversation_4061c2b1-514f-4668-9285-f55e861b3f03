import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:movie_map/feature/home/<USER>/mixin/home_bottom_mixin.dart';
import 'package:movie_map/feature/home/<USER>/widget/home_map.dart';
import 'package:movie_map/feature/shop/shop_view.dart';
import 'package:movie_map/product/init/language/locale_keys.g.dart';
import 'package:movie_map/product/state/provider/marker_manager.dart';
import 'package:movie_map/product/state/provider/navigation_provider.dart';
import 'package:movie_map/product/theme/theme.dart';
import 'package:provider/provider.dart';
part 'widget/home_app_bar.dart';
part 'widget/bottom_nav.dart';

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> {
  final String _scanButtonText = 'Scan Movies';
  bool isLoading = false;
  @override
  Widget build(BuildContext context) {
    final markerManager = Provider.of<MarkerManager>(context);

    return Scaffold(
      body: Stack(
        children: [
          Stack(
            children: [
              const MyMapScreen(),
              if (isLoading) ...[
                const Center(child: CircularProgressIndicator()),
              ],
            ],
          ),
          Positioned(
            left: 0,
            right: 0,
            top: 25,
            child: Center(
              child: GestureDetector(
                onTap: () async {
                  setState(() {
                    isLoading = true;
                  });

                  await markerManager.scanArea(
                    context,
                  );
                  setState(() {
                    isLoading = false;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: AppTheme.background,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        spreadRadius: 1,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    _scanButtonText,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.blue.shade600,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 20,
            // left: 0,
            right: 20,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (kDebugMode) ...[
                  const Gap(12),
                  // TODO: We do not have list for IOS yet.
                  if (Platform.isAndroid)
                    FloatingActionButton(
                      heroTag: 'shopPage',
                      backgroundColor: AppTheme.background,
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (_) => const ShopView(),
                          ),
                        );
                      },
                      child: Icon(
                        Icons.shopping_cart,
                        color: Colors.yellow.shade600,
                        size: 30,
                      ),
                    ),
                ],
                const Gap(12),
                FloatingActionButton(
                  heroTag: 'goToCurrentLocation',
                  backgroundColor: AppTheme.background,
                  onPressed: () {
                    final navigatorProvider = context.read<NavigationHelper>();
                    navigatorProvider.determinePosition(context).then(
                      (position) {
                        navigatorProvider.cameraToPosition(
                          LatLng(position.latitude, position.longitude),
                          16,
                          0,
                        );
                      },
                    );
                  },
                  child: Icon(
                    Icons.location_on,
                    color: Colors.grey.shade600,
                    size: 30,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
