import 'package:flutter/material.dart';
import 'package:movie_map/feature/profile/movie%20collection/movie_details_view.dart';
import 'package:movie_map/product/model/movie_model.dart';
import 'package:movie_map/product/state/provider/user_manager_provider.dart';
import 'package:provider/provider.dart';

class MovieCollectionView extends StatefulWidget {
  final List<MovieModel> movies;
  const MovieCollectionView({
    super.key,
    required this.movies,
  });

  @override
  State<MovieCollectionView> createState() => _MovieCollectionViewState();
}

class _MovieCollectionViewState extends State<MovieCollectionView> {
  final TextEditingController _searchController = TextEditingController();
  bool showWatchLater = false;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // `watchLaterMovies`'i sadece `build`'de alıyoruz
    final watchLaterMovies = context.select((UserManagerProvider userManagerProvider) => userManagerProvider.userModel?.watchLater) ?? [];

    // Filtrelemeyi burada yapıyoruz
    final filteredMovies = widget.movies.where((movie) {
      final matchesSearch = movie.title.toLowerCase().contains(_searchController.text.toLowerCase());
      final isInWatchLater = watchLaterMovies.contains(movie.id.toString());

      // Eğer showWatchLater aktifse, hem arama hem de watch later durumunu kontrol et
      if (showWatchLater) {
        return matchesSearch && isInWatchLater;
      } else {
        return matchesSearch;
      }
    }).toList();

    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          toolbarHeight: 56,
          actions: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.64,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                setState(() {
                  showWatchLater = !showWatchLater;
                });
              },
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: showWatchLater ? Colors.blue : Colors.grey.shade700,
                ),
                padding: const EdgeInsets.all(6),
                child: const Text(
                  'Watch Later',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
        body: filteredMovies.isNotEmpty
            ? ListView.builder(
                itemCount: filteredMovies.length,
                itemBuilder: (context, index) {
                  return MovieCard(
                    movie: filteredMovies[index],
                  );
                },
              )
            : const Center(
                child: Text(
                  'No movies found',
                  style: TextStyle(fontSize: 18),
                ),
              ),
      ),
    );
  }
}

class MovieCard extends StatelessWidget {
  final MovieModel movie;

  const MovieCard({
    super.key,
    required this.movie,
  });

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MovieDetailsView(
              movie: movie,
            ),
          ),
        );
      },
      child: Card(
        margin: const EdgeInsets.all(8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8.0),
              child: Image.network(
                'https://image.tmdb.org/t/p/w500${movie.posterPath}',
                width: 60,
                height: 80,
                fit: BoxFit.cover,
              ),
            ),
            SizedBox(width: screenWidth * 0.02),
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: screenWidth * 0.6,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          movie.title,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          movie.overview,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade800,
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 20,
                        ),
                        const SizedBox(width: 4),
                        Text(movie.voteAverage.toString().substring(0, 3)),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
